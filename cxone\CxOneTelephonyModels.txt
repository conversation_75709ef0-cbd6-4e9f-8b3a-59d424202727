%REM
	Library CxOneTelephonyModels
	Created Jun 11, 2025 by Code Signer/sehnp
	Description: Comments for Library
%END REM
Option Public
Option Declare



%REM
	Class CxOneTelephonyConfig
	Description: Comments for Class
%END REM
Public Class CxOneTelephonyConfig
	Public enabled As Boolean
	Public debug As Boolean
	Public logPayloads As Boolean
	Public dbCall As NotesDatabase
	Public dbCleo As NotesDatabase
	Public telephonyApiHost As String
	Public telephonyApiPath As String
	Public outboundCallPath As String
	Public callHistoryPath As String
	Public apiKey As String
End Class


%REM
	Class CxOneTelephonyConfig
	Description: 
	{
  "mode": 1,
  "firstName": "John",
  "lastName": "Doe",
  "callerFirstName": "Jane",
  "callerLastName": "Doe",
  "relationship": "Parent",
  "sexAtBirth": "Male",
  "dateOfbirth": "1985-06-15",
  "homeAddress": {
    "addressLine1": "10 Downing Street",
    "addressLine2": "",
    "addressLine3": "",
    "city": "London",
    "postCode": "SW1A 1AA"
  },
  "isCurrentAddress": true,
  "CurrentAddress": {
    "addressLine1": "",
    "addressLine2": "",
    "addressLine3": "",
    "city": "",
    "postCode": ""
  },
  "ethnicity": "White British",
  "chosenLanguage": "English",
  "preferredContact": "Phone",
  "consent": 2
}
%END REM
Public Class CxOneTelephonyInboundCaseDetails
	Public mode As Integer				'//  0 = None, 1 = Self, 2 = OnBehalfOf, 3 = Multiple (on behalf of)
	Public firstName As String
	Public lastName As String
	Public callerFirstName As String
	Public callerLastName As String
	Public relationship As String
	Public sexAtBirth As String			'//  "Male", "Female", or "Other"
	Public dateOfbirth As String		'//  YYYY-MM-DD 
	Public ethnicity As String
	Public chosenLanguage As String
	Public preferredContact As String	'//  e.g., "Phone", "Email", etc.
	Public consent As String			'//  0 = NotProvided, 1 = NotGiven, 2 = Given, 3 = NoCapacity
	Public isCurrentAddress As Boolean	'// true if same as home address
	Public homeAddress As CxOneTelephonyInboundCaseAddress
	Public currentAddress As CxOneTelephonyInboundCaseAddress
	
	Public returnTelephone As String
	Public symptoms As String
	
	%REM
		Sub New
		Description: Comments for Sub
	%END REM
	Public Sub New()
		
		Me.mode = -1
		
		Set Me.homeAddress = New CxOneTelephonyInboundCaseAddress()
		Set Me.currentAddress = New CxOneTelephonyInboundCaseAddress()
		
		Me.consent = False
		Me.isCurrentAddress = False
		
	End Sub
	
End Class







%REM
	Class CxOneTelephonyInboundCaseAddress
	Description: Comments for Class
%END REM
Public Class CxOneTelephonyInboundCaseAddress
	Public addressLine1 As String
	Public addressLine2 As String
	Public addressLine3 As String
	Public city As String
	Public postCode As String
End Class
%REM
	Class CxOneTelephonyInboundCaseNLP
	Description: Comments for Class
	
	}
		"mode": 1,
		"firstName": "John",
		"lastName": "Doe",
		"mobile": "+447890123456",
		"dateOfBirth": "1985-06-15", 
		"postCode": "SW1A 1AA",
		"symptoms":"headache, sweating"
	}
%END REM
Public Class CxOneTelephonyInboundCaseNLP
	Public mode As Integer				'//  0 = None, 1 = Self, 2 = OnBehalfOf, 3 = Multiple (on behalf of)
	
	Public firstName As String
	Public lastName As String
	Public mobile As String
	Public dateOfbirth As String		'//  YYYY-MM-DD 
	Public postCode As String
	Public symptoms As String
End Class


%REM
	Class UserTokens
	Description: Comments for Class
%END REM
Public Class UserTokens
	Public ltpaToken As String
	Public ssoToken As String
End Class