%REM
	Library CxOneTelephony
	Created Jun 11, 2025 by Code Signer/sehnp
	Description: Comments for Library
%END REM
Option Public
Option Declare




Use "String Buffer"
Use "MailLogSubclass"
Use "ResponseHelper"
Use "CxOneTelephonyModels"
Use "com.seh.cleo.CleoWrapperApi"
Use "JsonParserStandAlone"
Use "HelperJsonClasses"
Use "HttpConnectionObject"
Private cxOneTelephonyInstance As CxOneTelephony
%REM
	Class CxOneTelephony
	Description: Comments for Class
%END REM
Public Class CxOneTelephony
	
	Public config As CxOneTelephonyConfig
	Public session As NotesSession
	
	%REM
		Sub New
		Description: Comments for Sub
	%END REM
	Public Sub New(cxOneConfig As CxOneTelephonyConfig)
		Set Me.config = cxOneConfig
		Set Me.session = New NotesSession
	End Sub
	
	'/**
	'*	
	'*  
	'*/
	Public Function getUserTokens(userLookupKey As String) As UserTokens
		On Error GoTo ErrorHandler
	
		Set getUserTokens = New UserTokens()

		Dim docCleoPerson As NotesDocument
		Set docCleoPerson = Me.config.dbCleo.Getview("($person)").Getdocumentbykey(userLookupKey, True)
		
		If docCleoPerson Is Nothing Then
			Exit Function
		End If
		
		getUserTokens.ltpaToken = docCleoPerson.Getitemvalue("Audit_LtpaToken")(0)
		getUserTokens.ssoToken = docCleoPerson.Getitemvalue("ssoToken")(0)
	
	SimpleExit:
		Exit Function
	ErrorHandler:
		Call Me.simpleLogger(".getUserTokens() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	'/**
	'*	LtpaToken is HTTP only, so you don't have access via JS in the
	'*  browser.  The client has to make the connection to the socket/SSE.
	'*  N.B.  we are now exposing the HTTP only cookie via an api so now it's
	'*  available via JS in browser.  Yes, a bad actor could could copy.
	'*/
	Public Function getSocketLoginPayload() As ResponseHelper
		On Error GoTo ErrorHandler
		
		
		Call Me.simpleLogger(".getSocketLoginPayload() Start...")
		
		Set getSocketLoginPayload = New ResponseHelper()
		
		Dim s As New NotesSession
		Dim docCleoPerson As NotesDocument
		Dim nn As New NotesName(s.Effectiveusername)
		
		Dim userLookupKey As String
		userLookupKey = nn.Abbreviated
		
		Dim userTokens As UserTokens
		Set userTokens = Me.getUserTokens(userLookupKey)
		
		
		Set docCleoPerson = Me.config.dbCleo.Getview("($person)").Getdocumentbykey(userLookupKey, True)
		
		Dim data As New StringBuffer(16)
		
		Call data.Append(|{|)
		Call data.Append(|"ltpaToken": "| & userTokens.ltpaToken & |",|)
		Call data.Append(|"ssoToken": "| & userTokens.ssoToken & |"|)
		Call data.Append(|}|)
		
		getSocketLoginPayload.isSuccess = True
		getSocketLoginPayload.data = data.toString()
		
SimpleExit:
		Exit Function
ErrorHandler:
		getSocketLoginPayload.isSuccess = False
		getSocketLoginPayload.message = "Error Logged"
		Call Me.simpleLogger(".getSocketLoginPayload() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	'/**
	'*	
	'*  
	'*/
	Public Function processCreateCase(params As String) As ResponseHelper
		On Error GoTo ErrorHandler
		
		Set processCreateCase = New ResponseHelper()
		
		If Me.config.logPayloads Then
			Call logEvent("CxOneTelephony inbound payload: " & params, "", Nothing)
		End If
		
		Dim jsonStr As String
		jsonStr = params
		
		Dim cxOneCaseDetails As New CxOneTelephonyInboundCaseDetails()
		
		Dim jsonValue As String
		
		If Me.isDemoGraphicPayload(jsonStr) Then
			
			
		%REM
			Class CxOneTelephonyConfig
			Description: 
			{
			  "mode": 1,
			  "firstName": "John",
			  "lastName": "Doe",
			  "callerFirstName": "Jane",
			  "callerLastName": "Doe",
			  "relationship": "Parent",
			  "sexAtBirth": "Male",
			  "dateOfbirth": "1985-06-15",
			  "homeAddress": {
			    "addressLine1": "10 Downing Street",
			    "addressLine2": "",
			    "addressLine3": "",
			    "city": "London",
			    "postCode": "SW1A 1AA"
			  },
			  "isCurrentAddress": true,
			  "CurrentAddress": {
			    "addressLine1": "",
			    "addressLine2": "",
			    "addressLine3": "",
			    "city": "",
			    "postCode": ""
			  },
			  "ethnicity": "White British",
			  "chosenLanguage": "English",
			  "preferredContact": "Phone",
			  "consent": 2
			}
		%END REM
			
			jsonValue = GetJsonValue(jsonStr, "mode")
			Call Me.simpleLogger(".processCreateCase() mode: " & jsonValue)
			cxOneCaseDetails.mode = Val(jsonValue)
			
			jsonValue = GetJsonValue(jsonStr, "firstName")
			Call Me.simpleLogger(".processCreateCase() firstName: " & jsonValue)
			cxOneCaseDetails.firstName = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "lastName")
			Call Me.simpleLogger(".processCreateCase() lastName: " & jsonValue)
			cxOneCaseDetails.lastName = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "callerFirstName")
			Call Me.simpleLogger(".processCreateCase() callerFirstName: " & jsonValue)
			cxOneCaseDetails.callerFirstName = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "callerLastName")
			Call Me.simpleLogger(".processCreateCase() callerLastName: " & jsonValue)
			cxOneCaseDetails.callerLastName = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "relationship")
			Call Me.simpleLogger(".processCreateCase() relationship: " & jsonValue)
			cxOneCaseDetails.relationship = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "sexAtBirth")
			Call Me.simpleLogger(".processCreateCase() sexAtBirth: " & jsonValue)
			cxOneCaseDetails.sexAtBirth = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "dateOfbirth")
			Call Me.simpleLogger(".processCreateCase() dateOfbirth: " & jsonValue)
			cxOneCaseDetails.dateOfbirth = jsonValue
			
			'// <homeAddress>
			jsonValue = GetJsonValue(jsonStr, "homeAddress.addressLine1")
			Call Me.simpleLogger(".processCreateCase() homeAddress.addressLine1: " & jsonValue)
			cxOneCaseDetails.homeAddress.addressLine1 = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "homeAddress.addressLine2")
			Call Me.simpleLogger(".processCreateCase() homeAddress.addressLine2: " & jsonValue)
			cxOneCaseDetails.homeAddress.addressLine2 = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "homeAddress.addressLine3")
			Call Me.simpleLogger(".processCreateCase() homeAddress.addressLine3: " & jsonValue)
			cxOneCaseDetails.homeAddress.addressLine3 = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "homeAddress.city")
			Call Me.simpleLogger(".processCreateCase() homeAddress.city: " & jsonValue)
			cxOneCaseDetails.homeAddress.city = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "homeAddress.postCode")
			Call Me.simpleLogger(".processCreateCase() homeAddress.postCode: " & jsonValue)
			cxOneCaseDetails.homeAddress.postCode = jsonValue
			'// </homeAddress>
			
			
			Dim isCurrentAddress As Boolean
			
			isCurrentAddress = GetJsonValue(jsonStr, "isCurrentAddress")
			
			Call Me.simpleLogger(".processCreateCase() isCurrentAddress: " & isCurrentAddress)
			
			cxOneCaseDetails.isCurrentAddress = isCurrentAddress
			
			'// <CurrentAddress>
			jsonValue = GetJsonValue(jsonStr, "CurrentAddress.addressLine1")
			Call Me.simpleLogger(".processCreateCase() CurrentAddress.addressLine1: " & jsonValue)
			cxOneCaseDetails.CurrentAddress.addressLine1 = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "CurrentAddress.addressLine2")
			Call Me.simpleLogger(".processCreateCase() CurrentAddress.addressLine2: " & jsonValue)
			cxOneCaseDetails.CurrentAddress.addressLine2 = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "CurrentAddress.addressLine3")
			Call Me.simpleLogger(".processCreateCase() CurrentAddress.addressLine3: " & jsonValue)
			cxOneCaseDetails.CurrentAddress.addressLine3 = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "CurrentAddress.city")
			Call Me.simpleLogger(".processCreateCase() CurrentAddress.city: " & jsonValue)
			cxOneCaseDetails.CurrentAddress.city = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "CurrentAddress.postCode")
			Call Me.simpleLogger(".processCreateCase() CurrentAddress.postCode: " & jsonValue)
			cxOneCaseDetails.CurrentAddress.postCode = jsonValue
			'// </CurrentAddress>
			
			
			jsonValue = GetJsonValue(jsonStr, "ethnicity")
			Call Me.simpleLogger(".processCreateCase() ethnicity: " & jsonValue)
			cxOneCaseDetails.ethnicity = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "chosenLanguage")
			Call Me.simpleLogger(".processCreateCase() chosenLanguage: " & jsonValue)
			cxOneCaseDetails.chosenLanguage = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "preferredContact")
			Call Me.simpleLogger(".processCreateCase() preferredContact: " & jsonValue)
			cxOneCaseDetails.preferredContact = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "consent")
			Call Me.simpleLogger(".processCreateCase() consent: " & jsonValue)
			cxOneCaseDetails.consent = jsonValue
			
		Else
		%REM
				{
				  "mode": 1,
				  "firstName": "John",
				  "lastName": "Doe",
				  "mobile": "+447890123456",
				  "dateOfBirth": "1985-06-15", 
				  "postCode": "SW1A 1AA",
				  "symptoms":"headache, sweating"
				}
		%END REM
			
			jsonValue = GetJsonValue(jsonStr, "mode")
			Call Me.simpleLogger(".processCreateCase() mode: " & jsonValue)
			cxOneCaseDetails.mode = Val(jsonValue)
			
			jsonValue = GetJsonValue(jsonStr, "firstName")
			Call Me.simpleLogger(".processCreateCase() firstName: " & jsonValue)
			cxOneCaseDetails.firstName = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "lastName")
			Call Me.simpleLogger(".processCreateCase() lastName: " & jsonValue)
			cxOneCaseDetails.lastName = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "mobile")
			Call Me.simpleLogger(".processCreateCase() mobile: " & jsonValue)
			cxOneCaseDetails.returnTelephone = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "dateOfBirth")
			Call Me.simpleLogger(".processCreateCase() dateOfBirth: " & jsonValue)
			cxOneCaseDetails.dateOfBirth = jsonValue
			
			jsonValue = GetJsonValue(jsonStr, "postCode")
			Call Me.simpleLogger(".processCreateCase() postCode: " & jsonValue)
			cxOneCaseDetails.currentAddress.postCode = jsonValue

			jsonValue = GetJsonValue(jsonStr, "symptoms")
			Call Me.simpleLogger(".processCreateCase() symptoms: " & jsonValue)
			cxOneCaseDetails.symptoms = jsonValue
			
		End If
		
		Set processCreateCase = Me.createCleoCase(cxOneCaseDetails)
		
SimpleExit:
		Exit Function
ErrorHandler:
		processCreateCase.isSuccess = False
		processCreateCase.message = "Error Logged"
		Call Me.simpleLogger(".processCreateCase() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	'/**
	'*	
	'*  
	'*/
	Public Function getJsonGracefully(jsonStr As String, propPath As String) As Variant
		On Error GoTo ErrorHandler
		
		Dim jsonValue As Variant
		jsonValue = GetJsonValue(jsonStr, propPath)
		
		getJsonGracefully = jsonValue
		
SimpleExit:
		Exit Function
ErrorHandler:
		Set getJsonGracefully = Nothing
		Resume SimpleExit
	End Function	
	
	'/**
	'*	
	'*  
	'*/
	Public Function isDemoGraphicPayload(jsonStr As String) As Boolean
		On Error GoTo ErrorHandler
		
		Dim jsonValue As Variant
		jsonValue = GetJsonValue(jsonStr, "homeAddress.addressLine1")
		
		isDemoGraphicPayload = True
		
		
SimpleExit:
		Exit Function
ErrorHandler:
		isDemoGraphicPayload = False
		Resume SimpleExit
	End Function
	
	'/**
	'*	
	'*  
	'*/
	Public Function createCleoCase(inboundCaseDetails As CxOneTelephonyInboundCaseDetails) As ResponseHelper
		On Error GoTo ErrorHandler
		
		Call Me.simpleLogger(".createCleoCase() Start...")
		
		Set createCleoCase = New ResponseHelper()
		
		Dim docCase As NotesDocument
		Dim callDocument As CallDocument
		
		
		Set docCase = Me.config.dbCall.Createdocument()

		
		
		%REM
	Class CxOneTelephonyConfig
	Description: 
	{
  "mode": 1,
  "firstName": "John",
  "lastName": "Doe",
  "callerFirstName": "Jane",
  "callerLastName": "Doe",
  "relationship": "Parent",
  "sexAtBirth": "Male",
  "dateOfbirth": "1985-06-15",
  "homeAddress": {
    "addressLine1": "10 Downing Street",
    "addressLine2": "",
    "addressLine3": "",
    "city": "London",
    "postCode": "SW1A 1AA"
  },
  "isCurrentAddress": true,
  "CurrentAddress": {
    "addressLine1": "",
    "addressLine2": "",
    "addressLine3": "",
    "city": "",
    "postCode": ""
  },
  "ethnicity": "White British",
  "chosenLanguage": "English",
  "preferredContact": "Phone",
  "consent": 2
}
		%END REM
		
		Call Me.simpleLogger(".createCleoCase() Patient Demographics...")
		
		
		Call docCase.Replaceitemvalue("Form", "Call")
		
		'// Total legacy issue, this needs at least 1 field on the call.
		Set callDocument = New CallDocument(docCase)
		
		Dim itm As NotesItem
		
		Set itm = New NotesItem( docCase, "Authors", "*", AUTHORS )
		If Not( itm.IsAuthors ) Then
			itm.IsAuthors = True
		End If
		
		
		Call docCase.Replaceitemvalue("CxOneMode", inboundCaseDetails.mode)
		
		Call docCase.Replaceitemvalue("CallForename", inboundCaseDetails.firstName)
		Call docCase.Replaceitemvalue("CallSurname", inboundCaseDetails.lastName)
		
		Call docCase.Replaceitemvalue("CallCName", inboundCaseDetails.callerFirstName + " " + inboundCaseDetails.callerLastName)
		
		Call docCase.Replaceitemvalue("CallCRel", inboundCaseDetails.relationship)
		Call docCase.Replaceitemvalue("CallMF", inboundCaseDetails.sexAtBirth)
		
		Call docCase.Replaceitemvalue("CallDob", "")
		
		Call Me.simpleLogger(".createCleoCase() Patient Demographics DOB...")
		
		Dim ndtDob As NotesDateTime
		Call Me.simpleLogger(".createCleoCase() Patient Demographics DOB: " & inboundCaseDetails.dateOfbirth)
		If inboundCaseDetails.dateOfbirth <> "" Then
			'// "1985-06-15
			Dim dobParts As Variant
			dobParts = Split(inboundCaseDetails.dateOfbirth & "", "-")
			
			Dim dobVar As Variant
			dobVar = DateNumber(CLng(dobParts(0)), CLng(dobParts(1)), CLng(dobParts(2)))
			
			Call docCase.Replaceitemvalue("CallDob", dobVar)
		End If
		
		Call Me.simpleLogger(".createCleoCase() Patient Home address...")
		
		'// <home_address>
		Call docCase.Replaceitemvalue("PatientAddress1", inboundCaseDetails.homeAddress.addressLine1)
		Call docCase.Replaceitemvalue("PatientAddress2", inboundCaseDetails.homeAddress.addressLine2)
		Call docCase.Replaceitemvalue("PatientAddress3", inboundCaseDetails.homeAddress.addressLine3)
		Call docCase.Replaceitemvalue("PatientTown", inboundCaseDetails.homeAddress.city)
		Call docCase.Replaceitemvalue("PatientPostCode", inboundCaseDetails.homeAddress.postCode)
		'// </home_address>
		
		Call Me.simpleLogger(".createCleoCase() Patient Current address...")
		
		'// <CurrentAddress>
		If inboundCaseDetails.isCurrentAddress Then
			Call docCase.Replaceitemvalue("CallAddress1", inboundCaseDetails.homeAddress.addressLine1)
			Call docCase.Replaceitemvalue("CallAddress2", inboundCaseDetails.homeAddress.addressLine2)
			Call docCase.Replaceitemvalue("CallAddress3", inboundCaseDetails.homeAddress.addressLine3)
			Call docCase.Replaceitemvalue("CallTown", inboundCaseDetails.homeAddress.city)
			Call docCase.Replaceitemvalue("CallPostCode", inboundCaseDetails.homeAddress.postCode)
		Else
			Call docCase.Replaceitemvalue("CallAddress1", inboundCaseDetails.currentAddress.addressLine1)
			Call docCase.Replaceitemvalue("CallAddress2", inboundCaseDetails.currentAddress.addressLine2)
			Call docCase.Replaceitemvalue("CallAddress3", inboundCaseDetails.currentAddress.addressLine3)
			Call docCase.Replaceitemvalue("CallTown", inboundCaseDetails.currentAddress.city)
			Call docCase.Replaceitemvalue("CallPostCode", inboundCaseDetails.currentAddress.postCode)	
		End If
		
		'// </CurrentAddress>
		
		Call Me.simpleLogger(".createCleoCase() Other...")
		
		Call docCase.Replaceitemvalue("CallEthnicity", inboundCaseDetails.ethnicity)
		Call docCase.Replaceitemvalue("CallLanguage", inboundCaseDetails.chosenLanguage)
		
		Call docCase.Replaceitemvalue("PreferredContact", inboundCaseDetails.preferredContact)
		
		If inboundCaseDetails.consent Then
			Call docCase.Replaceitemvalue("ElectronicRecordConsent", "Yes")			
		Else
			Call docCase.Replaceitemvalue("ElectronicRecordConsent", "No")
		End If
		
		
		Call docCase.Replaceitemvalue("CallTelNo_R", inboundCaseDetails.returnTelephone)
		
		Call docCase.Replaceitemvalue("CallSymptoms", inboundCaseDetails.symptoms)
		
		'//  <Fields_To_Make_CLEO_Case_Work>
		Call docCase.Replaceitemvalue("CallStatusValue", -100)
		
		Call callDocument.SetCallNumber()
		
		Dim sbResponse As New StringBuffer(16)
		
		Call sbResponse.Append(|{|)
		Call sbResponse.Append(|"caseID": | & docCase.Getitemvalue("CallNo")(0))
		Call sbResponse.Append(|}|)
		createCleoCase.data = sbResponse.toString()
		
		'Call me.CallObj.setBaseInformation(doc.Getitemvalue("CallPostCode")(0))
		
		'// Service?
		
		'// classification
		
		'//  </Fields_To_Make_CLEO_Case_Work>
		
		
		Call callDocument.setViewRowData()
		Call callDocument.auditMeNow("START")
		
		Call docCase.Save(True, False)
		createCleoCase.isSuccess = True
		
		Call Me.simpleLogger(".createCleoCase() Finished.")
		
SimpleExit:
		Exit Function
ErrorHandler:
		createCleoCase.isSuccess = False
		createCleoCase.message = "Error Logged"
		Call Me.simpleLogger(".createCleoCase() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	%REM
		Function getNewCallDemographics
		Description: Comments for Function
	%END REM
	Public Function getNewCallDemographics(caseId As Long) As ResponseHelper
		
		Call Me.simpleLogger(".getNewCallDemographics() Start...")
		
		Set getNewCallDemographics = New ResponseHelper()
		
		Dim docCall As NotesDocument
		Dim sb As New StringBuffer(16)
		
		
		Call Me.simpleLogger(".getNewCallDemographics() Get View...")
		Set docCall = Me.config.dbCall.Getview("($CxOneDrafts)").Getdocumentbykey(caseId, True)
		Call Me.simpleLogger(".getNewCallDemographics() Get View, success...")
		
		If docCall Is Nothing Then
			Call Me.simpleLogger(".getNewCallDemographics() case " & caseId & " not found...")
			getNewCallDemographics.isSuccess = False
			getNewCallDemographics.message = "Case: " & caseId & " could not be found."
			getNewCallDemographics.data = |{}|
			Exit Function 
		Else
			
			Call Me.simpleLogger(".getNewCallDemographics() case " & caseId & " found...")
			
			Dim je As New JsonEncoder()
			
			Call sb.Append(|{|)
			
			Call sb.Append(|"CallNo": | & je.JsonEncode(docCall.Getitemvalue("CallNo")(0)) & |,|)
			Call sb.Append(|"CxOneMode": | & je.JsonEncode(docCall.Getitemvalue("CxOneMode")(0)) & |,|)
			Call sb.Append(|"CallStatusValue": | & je.JsonEncode(docCall.Getitemvalue("CallStatusValue")(0)) & |,|)
			
			Call sb.Append(|"CallForename": "| & je.JsonEncode(docCall.Getitemvalue("CallForename")(0)) & |",|)
			Call sb.Append(|"CallSurname": "| & je.JsonEncode(docCall.Getitemvalue("CallSurname")(0)) & |",|)
			Call sb.Append(|"CallMF": "| & je.JsonEncode(docCall.Getitemvalue("CallMF")(0)) & |",|)
			
			Call sb.Append(|"CallAddress1": "| & je.JsonEncode(docCall.Getitemvalue("CallAddress1")(0)) & |",|)
			Call sb.Append(|"CallAddress2": "| & je.JsonEncode(docCall.Getitemvalue("CallAddress2")(0)) & |",|)
			Call sb.Append(|"CallAddress3": "| & je.JsonEncode(docCall.Getitemvalue("CallAddress3")(0)) & |",|)
			Call sb.Append(|"CallTown": "| & je.JsonEncode(docCall.Getitemvalue("CallTown")(0)) & |",|)
			Call sb.Append(|"CallPostCode": "| & je.JsonEncode(docCall.Getitemvalue("CallPostCode")(0)) & |",|)
			
			Call sb.Append(|"PatientAddress1": "| & je.JsonEncode(docCall.Getitemvalue("PatientAddress1")(0)) & |",|)
			Call sb.Append(|"PatientAddress2": "| & je.JsonEncode(docCall.Getitemvalue("PatientAddress2")(0)) & |",|)
			Call sb.Append(|"PatientAddress3": "| & je.JsonEncode(docCall.Getitemvalue("PatientAddress3")(0)) & |",|)
			Call sb.Append(|"PatientTown": "| & je.JsonEncode(docCall.Getitemvalue("PatientTown")(0)) & |",|)
			Call sb.Append(|"PatientPostCode": "| & je.JsonEncode(docCall.Getitemvalue("PatientPostCode")(0)) & |",|)
			
			Call sb.Append(|"CallCName": "| & je.JsonEncode(docCall.Getitemvalue("CallCName")(0)) & |",|)
			Call sb.Append(|"CallCRel": "| & je.JsonEncode(docCall.Getitemvalue("CallCRel")(0)) & |",|)
			Call sb.Append(|"CallDOB": "| & je.JsonEncode(docCall.Getitemvalue("CallDOB")(0)) & |",|)
			Call sb.Append(|"CallEthnicity": "| & je.JsonEncode(docCall.Getitemvalue("CallEthnicity")(0)) & |",|)
			Call sb.Append(|"CallLanguage": "| & je.JsonEncode(docCall.Getitemvalue("CallLanguage")(0)) & |",|)
			Call sb.Append(|"ElectronicRecordConsent": "| & je.JsonEncode(docCall.Getitemvalue("ElectronicRecordConsent")(0)) & |",|)
			Call sb.Append(|"CallSymptoms": "| & je.JsonEncode(docCall.Getitemvalue("CallSymptoms")(0)) & |",|)
			Call sb.Append(|"CallTelNo_R": "| & je.JsonEncode(docCall.Getitemvalue("CallTelNo_R")(0)) & |",|)
			
			Call sb.Append(|"PreferredContact": "| & je.JsonEncode(docCall.Getitemvalue("PreferredContact")(0)) & |"|)
			
			Call sb.Append(|}|)
			
			
			'// Let's stamp the case with the user that "picked it up"
			Dim s As New NotesSession
			Dim callDocument As New CallDocument(docCall)
			
			Call docCall.Replaceitemvalue("CxOne_PickUpUser", s.Effectiveusername)
			Call docCall.Replaceitemvalue("CxOne_PickUpTime", Now)
			Call docCall.Save(True, False)
			
			Call callDocument.auditMeNow("START")
			
		End If
		
		
		getNewCallDemographics.data = sb.toString()
		getNewCallDemographics.isSuccess = True
		
		Call Me.simpleLogger(".getNewCallDemographics() Finish.")
		
SimpleExit:
		Exit Function
ErrorHandler:
		getNewCallDemographics.isSuccess = False
		getNewCallDemographics.message = "Error Logged"
		getNewCallDemographics.data = |{}|
		Call Me.simpleLogger(".createCleoCase() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function


	%REM
		Function makeOutboundCall
		Description: Makes an outbound call via CXOne Platform
		API: POST api/v1/cases/call/{caseID}/{userPrincipalName}?phoneNo={phoneNo}
	%END REM
	Public Function makeOutboundCall(caseId As Long, userPrincipalName As String, phoneNo As String) As ResponseHelper
		On Error GoTo ErrorHandler

		Call Me.simpleLogger(".makeOutboundCall() Start...")

		Set makeOutboundCall = New ResponseHelper()

		' Validate required parameters
		If caseId <= 0 And phoneNo = "" Then
			makeOutboundCall.isSuccess = False
			makeOutboundCall.message = "Either caseID or phoneNo must be provided"
			makeOutboundCall.data = |{}|
			Exit Function
		End If

		If userPrincipalName = "" Then
			makeOutboundCall.isSuccess = False
			makeOutboundCall.message = "userPrincipalName is required"
			makeOutboundCall.data = |{}|
			Exit Function
		End If

		' Validate userPrincipalName format (should be email with sehnp.co.uk domain)
		If InStr(userPrincipalName, "@sehnp.co.uk") = 0 Then
			makeOutboundCall.isSuccess = False
			makeOutboundCall.message = "userPrincipalName must be a valid sehnp.co.uk email address"
			makeOutboundCall.data = |{}|
			Exit Function
		End If

		' Build the API URL
		Dim fullApiPath As String
		fullApiPath = Me.config.telephonyApiHost & Me.config.outboundCallPath & "/" & caseId & "/" & userPrincipalName

		If phoneNo <> "" Then
			fullApiPath = fullApiPath & "?phoneNo=" & phoneNo
		End If

		Call Me.simpleLogger(".makeOutboundCall() fullApiPath: " & fullApiPath)

		' Make HTTP POST request to CXOne API
		Dim httpConnectionObject As New HttpConnectionObject()
		httpConnectionObject.debug = Me.config.debug

		Dim httpObject As Variant
		Set httpObject = httpConnectionObject.getConnectionObject()

		Call Me.simpleLogger(".makeOutboundCall() Open (POST): " & fullApiPath)
		Call httpObject.open("POST", fullApiPath, False)

		Call Me.simpleLogger(".makeOutboundCall() set headers...")
		Call httpObject.setRequestHeader("Content-Type", "application/json")

		' Add authentication headers if configured
		Call Me.addHttpHeaders(httpObject)

		' No request body for this API
		Dim payload As String
		payload = ""

		Call Me.simpleLogger(".makeOutboundCall() Send request...")
		Call httpObject.send(payload)

		Call Me.simpleLogger(".makeOutboundCall() Response status: " & httpObject.status)
		Call Me.simpleLogger(".makeOutboundCall() Response text: " & httpObject.responseText)

		' Process response
		If httpObject.status = 200 Or httpObject.status = 201 Then
			makeOutboundCall.isSuccess = True
			makeOutboundCall.message = "Call initiated successfully"
			makeOutboundCall.data = httpObject.responseText
		Else
			makeOutboundCall.isSuccess = False
			makeOutboundCall.message = "HTTP Error " & httpObject.status & ": " & httpObject.statusText
			makeOutboundCall.data = httpObject.responseText
		End If

		Call Me.simpleLogger(".makeOutboundCall() Finished.")

SimpleExit:
		Exit Function
ErrorHandler:
		makeOutboundCall.isSuccess = False
		makeOutboundCall.message = "Error Logged: " & Error
		makeOutboundCall.data = |{}|
		Call Me.simpleLogger(".makeOutboundCall() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function


	%REM
		Function getCxOneCallHistory
		Description: Gets call history for a case from CXOne Platform
		API: POST api/v1/cases/call/history/{caseID}
	%END REM
	Public Function getCxOneCallHistory(caseId As Long) As ResponseHelper
		On Error GoTo ErrorHandler

		Call Me.simpleLogger(".getCxOneCallHistory() Start...")

		Set getCxOneCallHistory = New ResponseHelper()

		' Validate required parameters
		If caseId <= 0 Then
			getCxOneCallHistory.isSuccess = False
			getCxOneCallHistory.message = "caseID is required and must be greater than 0"
			getCxOneCallHistory.data = |{}|
			Exit Function
		End If

		' Build the API URL
		Dim fullApiPath As String
		fullApiPath = Me.config.telephonyApiHost & Me.config.callHistoryPath & "/" & caseId

		Call Me.simpleLogger(".getCxOneCallHistory() fullApiPath: " & fullApiPath)

		' Make HTTP POST request to CXOne API
		Dim httpConnectionObject As New HttpConnectionObject()
		httpConnectionObject.debug = Me.config.debug

		Dim httpObject As Variant
		Set httpObject = httpConnectionObject.getConnectionObject()

		Call Me.simpleLogger(".getCxOneCallHistory() Open (POST): " & fullApiPath)
		Call httpObject.open("POST", fullApiPath, False)

		Call Me.simpleLogger(".getCxOneCallHistory() set headers...")
		Call httpObject.setRequestHeader("Content-Type", "application/json")
		
		' Add authentication headers if configured
		Call Me.addHttpHeaders(httpObject)
		
		' No request body for this API
		Dim payload As String
		payload = ""

		Call Me.simpleLogger(".getCxOneCallHistory() Send request...")
		Call httpObject.send(payload)

		Call Me.simpleLogger(".getCxOneCallHistory() Response status: " & httpObject.status)
		Call Me.simpleLogger(".getCxOneCallHistory() Response text: " & httpObject.responseText)

		' Process response
		If httpObject.status = 200 Or httpObject.status = 201 Then
			getCxOneCallHistory.isSuccess = True
			getCxOneCallHistory.message = "Call history retrieved successfully"
			getCxOneCallHistory.data = httpObject.responseText
		Else
			getCxOneCallHistory.isSuccess = False
			getCxOneCallHistory.message = "HTTP Error " & httpObject.status & ": " & httpObject.statusText
			getCxOneCallHistory.data = httpObject.responseText
		End If

		Call Me.simpleLogger(".getCxOneCallHistory() Finished.")

SimpleExit:
		Exit Function
ErrorHandler:
		getCxOneCallHistory.isSuccess = False
		getCxOneCallHistory.message = "Error Logged: " & Error
		getCxOneCallHistory.data = |{}|
		Call Me.simpleLogger(".getCxOneCallHistory() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	
	%REM
		Sub addHttpHeaders
		Description: Comments for Sub
	%END REM
	Public Sub addHttpHeaders(httpObject As Variant)
		On Error GoTo ErrorHandler
				
		Dim nn As New NotesName(Me.session.Effectiveusername)
		
		Dim userLookupName As String
		userLookupName = nn.Abbreviated
		
		Dim userTokens As UserTokens
		Set userTokens = Me.getUserTokens(userLookupName)

		Call httpObject.setRequestHeader("X-LtpaToken", userTokens.ltpaToken)
		Call httpObject.setRequestHeader("X-SsoToken", userTokens.ssoToken)

		' Add authentication headers if configured
		If Me.config.apiKey <> "" Then
			Call httpObject.setRequestHeader("X-API-Key", Me.config.apiKey)
		End If
		
SimpleExit:
		Exit Sub
ErrorHandler:
		Call Me.simpleLogger(".addHttpHeaders() " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Sub
	
	'/**
	'*	
	'*  
	'*/
	Public Sub simpleLogger(message As String)
		If Me.config.debug Then
			MsgBox "CxOneTelephony " & message
		End If
	End Sub
	
End Class

Public Function getCxOneTelephonyInstance(configDoc As NotesDocument) As CxOneTelephony
    On Error GoTo ErrorHandler
    
	If cxOneTelephonyInstance Is Nothing Then
        Dim session As New NotesSession
		Dim config As New CxOneTelephonyConfig

        ' Set up configuration
	    config.enabled = configDoc.GetItemValue("CxOne_Enabled")(0) = "1"
		config.debug = configDoc.GetItemValue("CxOne_Debug")(0) = "1"
		config.logPayloads = configDoc.GetItemValue("CxOne_LogPayload")(0) = "1"

		config.telephonyApiHost = configDoc.GetItemValue("CxOne_Host")(0)
		config.telephonyApiPath = configDoc.GetItemValue("CxOne_ApiPath")(0)
		config.outboundCallPath = configDoc.GetItemValue("CxOne_OutboundCallPath")(0)
		config.callHistoryPath = configDoc.GetItemValue("CxOne_CallHistoryPath")(0)
		config.apiKey = configDoc.GetItemValue("CxOne_ApiKey")(0)

		' Set default paths if not configured
		If config.outboundCallPath = "" Then
			config.outboundCallPath = "/api/v1/cases/call"
		End If

		If config.callHistoryPath = "" Then
			config.callHistoryPath = "/api/v1/cases/call/history"
		End If


		If config.debug Then
			MsgBox "getCxOneTelephonyInstance() enabled: " & config.enabled
			MsgBox "getCxOneTelephonyInstance() debug: " & config.debug
			MsgBox "getCxOneTelephonyInstance() logPayloads: " & config.logPayloads
			MsgBox "getCxOneTelephonyInstance() telephonyApiHost: " & config.telephonyApiHost
			MsgBox "getCxOneTelephonyInstance() telephonyApiPath: " & config.telephonyApiPath
			MsgBox "getCxOneTelephonyInstance() outboundCallPath: " & config.outboundCallPath
			MsgBox "getCxOneTelephonyInstance() callHistoryPath: " & config.callHistoryPath
			If config.apiKey <> "" Then
				MsgBox "getCxOneTelephonyInstance() apiKey: " & Left(config.apiKey, 10) & "..."
			Else
				MsgBox "getCxOneTelephonyInstance() apiKey: [NOT CONFIGURED]"
			End If
		End If

	    Set config.dbCall = session.CurrentDatabase
		Set config.dbCleo = New NotesDatabase(session.Currentdatabase.Server, configDoc.Getitemvalue("Config_CLEOWEB_Path")(0))

		Set cxOneTelephonyInstance = New CxOneTelephony(config)
    End If
    
	Set getCxOneTelephonyInstance = cxOneTelephonyInstance
   Exit Function
    
ErrorHandler:
    MsgBox "Error in getAppointmentSystemInstance: " & Error
    Resume Next
End Function

























