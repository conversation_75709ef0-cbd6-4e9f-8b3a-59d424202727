%REM
	Library CleoSms
	Created May 2, 2025 by Code Signer/sehnp
	Description: Comments for Library
%END REM
Option Public
Option Declare

Use "CleoSmsModels"
Use "CleoCallLowLevelUtility"
Use "MailLogSubclass"
Use "ResponseHelper"
Use "HttpConnectionObject"
Use "string buffer"
Use "HelperJsonClasses"
Use "TelephoneHelper"
Use "ArrayHelper"





Private cleoSmsHelper As CleoSms
%REM
	Class CleoSms
	Description: Comments for Class
%END REM
Public Class CleoSms
	
	Public config As CleoSmsConfig
	
	Public s As NotesSession
	
	%REM
		Sub New
		Description: Comments for Sub
	%END REM
	Public Sub New(cleoSmsConfig As CleoSmsConfig)
		Set Me.s = New NotesSession
		Set Me.config = cleoSmsConfig
	End Sub
	
	
	Public Function validateSms(CleoSmsMessage As CleoSmsMessage) As ResponseHelper
		On Error GoTo ErrorHandler
		
		Set validateSms = New ResponseHelper()
		validateSms.isSuccess = True
		
'		validateSms.isSuccess = False
'		Exit function
		
		If CleoSmsMessage.clientName = "" Then
			validateSms.uniqueIdentifier = "CLIENT_NAME_EMPTY"
			validateSms.message = validateSms.message & "Client name required;"
			validateSms.isSuccess = False
		End If
		
		If CleoSmsMessage.fromUser = "" Then
			validateSms.uniqueIdentifier = "FROM_USER_EMPTY"
			validateSms.message = validateSms.message & "From user required;"
			validateSms.isSuccess = False
		End If
		
		If CleoSmsMessage.message = "" Then
			validateSms.uniqueIdentifier = "MESSAGE_EMPTY"
			validateSms.message = validateSms.message & "Message required;"
			validateSms.isSuccess = False
		End If
		
		If Len(CleoSmsMessage.message) > Me.config.textMaxChars Then
			validateSms.uniqueIdentifier = "MESSAGE_TOO_LONG"
			validateSms.message = validateSms.message & "Message length cannot exceed " & Me.config.textMaxChars & " characters;"
			validateSms.isSuccess = False
		End If
		
		'// Let remote service validate
		%rem
		If CleoSmsMessage.mobileNumber = "" Then
			validateSms.uniqueIdentifier = "TEL_NUMBER_EMPTY"
			validateSms.message = validateSms.message & "Telephone number required;"
			validateSms.isSuccess = False
		Else
			Dim telephoneHelper As New TelephoneHelper()
			
			If telephoneHelper.IsValidUKPhoneNumber(CleoSmsMessage.mobileNumber) = False Then
				'// we can probably be quite lax with this and let the SMS service
				'// deal with validation, else we might fail inadvertantly.
				validateSms.uniqueIdentifier = "TEL_NUMBER_INVALID"
				validateSms.message = validateSms.message & "Telephone number invalid;"
				validateSms.isSuccess = False
			End If
			
		End If
		%end rem
		
		If CleoSmsMessage.productName = "" Then
			validateSms.uniqueIdentifier = "PRODUCT_NAME_EMPTY"
			validateSms.message = validateSms.message & "Client name required;"
			validateSms.isSuccess = False
		End If
		
		If CleoSmsMessage.referenceId = "" Then
			validateSms.uniqueIdentifier = "REF_ID_EMPTY"
			validateSms.message = validateSms.message & "Reference ID required;"
			validateSms.isSuccess = False
		End If
		
		
SimpleExit:
		Exit Function
ErrorHandler:
		validateSms.isSuccess = False
		validateSms.uniqueIdentifier = "ERROR"
		validateSms.message = "Error, see log."
		Call Me.simpleLogger("CleoSms.validateSms() ERROR: " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	%REM
		Function sendSms
		Description: Comments for Function
	%END REM
	Public Function sendSms(CleoSmsMessage As CleoSmsMessage) As ResponseHelper
		On Error GoTo ErrorHandler
		
		Set sendSms = New ResponseHelper()
		
		Dim validationResult As ResponseHelper
		Set validationResult = Me.validateSms(Cleosmsmessage)
		If validationResult.isSuccess = False Then
			Set sendSms = validationResult
			Exit Function
		End If
		
		
		Dim httpConnectionObject As New HttpConnectionObject()
		dim je As New JsonEncoder()
		
		Dim payload As String
		Dim sb As New StringBuffer(16)
		
		Call sb.Append(|{|)
		Call sb.Append(|"clientName": "| & je.JsonEncode(CleoSmsMessage.clientName) & |",|)
		Call sb.Append(|"productName": "| & je.JsonEncode(CleoSmsMessage.productName) & |",|)
		Call sb.Append(|"referenceId": "| & je.JsonEncode(CleoSmsMessage.referenceId) & |",|)
		Call sb.Append(|"message": "| & je.JsonEncode(CleoSmsMessage.message) & |",|)
		Call sb.Append(|"mobileNumber": "| & je.JsonEncode(CleoSmsMessage.mobileNumber) & |",|)
		Call sb.Append(|"from": "| & je.JsonEncode(CleoSmsMessage.fromUser) & |"|)
		
		Call sb.Append(|}|)
		
		payload = sb.toString()
		
		Dim fullApiPath As String
		
		'// don't need a "/" between host and path, do that in the config
		fullApiPath = Me.config.smsEndPoint & Me.config.sendPath
		
		Call Me.simpleLogger("sendSms() fullApiPath: " & fullApiPath)
		
		Dim httpObject As Variant
		Set httpObject = httpConnectionObject.getConnectionObject()
		
		Call Me.simpleLogger("HttpConnectionObject.doPostwithJwt() Open (POST): " & fullApiPath)
		Call httpObject.open("POST", fullApiPath, False)
		
		Call Me.simpleLogger("HttpConnectionObject.doPostwithJwt() set headers...")
		Call httpObject.setRequestHeader("Content-Type", "application/json")
		httpObject.SetRequestHeader "Content-Length", Len(payload)
		
		'Prashant Raj 02/05/2025 10:59 • X-API-Key","value":"67ec5e4dce0f30c296a581638646febe"
		Call Me.simpleLogger("HttpConnectionObject.doPostwithJwt() X-API-Key: " & Me.config.apiKey)
		Call httpObject.setRequestHeader("X-API-Key", Me.config.apiKey)
		
		Call Me.simpleLogger("HttpConnectionObject.doPostwithJwt() Send...payload: " & payload)
		Call httpObject.send(payload & "")
		
		Dim responseStatus As Variant
		
		responseStatus = Val(httpObject.status)
		Call Me.simpleLogger("HttpConnectionObject.doPostwithJwt() responseStatus: " & responseStatus)
		
		Dim responseBody As String
		responseBody = httpObject.responseText
		Call Me.simpleLogger("HttpConnectionObject.doPostwithJwt() responseBody: " & responseBody)
		
		If responseStatus < 200 Or responseStatus >= 300 Then
			sendSms.isSuccess = False
			
			Call logErrorEx("SMS payload: " & payload &_
			", fullApiPath: " & fullApiPath &_
			 ", responseStatus: " &_
			responseStatus & ", response: " & responseBody, SEVERITY_MEDIUM, Nothing)
			
		Else
			'// we got a successful repsonse, not necesasarily a successful send.
			sendSms.isSuccess = True
			
			If Me.config.logPayloads Then
				Call logEvent("SMS payload: " & payload &_
				", fullApiPath: " & fullApiPath &_
				", responseStatus: " &_
				responseStatus & ", response: " & responseBody, SEVERITY_MEDIUM, Nothing)
			End If
			
		End If
		
		'// TODO should we send the response back if a failure...!!!??  Yes, so user knows what's wrong, but...
		'// you don't wnat to be sending back stuff like this: "Insufficient credit, Not enough credits to use this service."
		'// is there range of HTTP codes we can check?
		sendSms.data = responseBody
		
		'// as in...CLEO successfully communicated with SMS service...
		'// but it might have returned some errors.  Client will display
		sendSms.isSuccess = True
		

		
		%REM
			{
  "clientName": "Nick",
  "productName": "CLEOTest",
  "referenceId": "12345",
  "message": "Hi",
  "mobileNumber": "07912626865",
  "from": "NhsNoTest"
}
		%END REM
		
		
		
		
		'HttpConnectionObject
		
		
		%REM
{
  "success": false,
  "message": "Some parts failed to send. Check logs.",
  "errorType": null,
  "statusCode": 507,
  "responses": [
    "Insufficient credit, Not enough credits to use this service."
  ]
}

{
  "success": true,
  "message": "string",
  "errorType": "string",
  "statusCode": 0,
  "responses": [
    "string"
  ]
}
		%END REM
	
		
SimpleExit:
		Exit Function
ErrorHandler:
		sendSms.isSuccess = False
		sendSms.uniqueIdentifier = "ERROR"
		sendSms.message = "Error, see log."
		Call Me.simpleLogger("CleoSms.sendSms() ERROR: " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	%REM
		Function processInboutnItk
		Description: Comments for Function
	%END REM
	Public Function processInboutnItk(docCall As NotesDocument) As ResponseHelper
		On Error GoTo ErrorHandler
		
		Set processInboutnItk = New ResponseHelper()
		
		Dim arrayHelper As New ArrayHelper()
		Dim callService As String
		Dim returnTelNumber As String
		
		If Me.config.enabled  = False Then
			processInboutnItk.isSuccess = False
			processInboutnItk.uniqueIdentifier = "ERROR"
			processInboutnItk.message = "Error, see log."
		End If
		
		callService = UCase(docCall.Getitemvalue("CallService")(0))
		returnTelNumber = UCase(docCall.Getitemvalue("CallTelNo_R")(0))
		
		If returnTelNumber = "" Then
			processInboutnItk.isSuccess = False
			processInboutnItk.uniqueIdentifier = "NO_TEL_NO_PRESENT"
			Exit Function
		End If
		
		Call Me.simpleLogger("CleoSms.processInboutnItk() service: " & callService)
		If arrayHelper.arrayContains(Me.config.itkInServices, callService) Then
			Call Me.simpleLogger("CleoSms.processInboutnItk() SMS: send REQUIRED for service: " & callService)
			
			Dim cleoSmsMessage As New CleoSmsMessage()
			
			cleoSmsMessage.clientName = Me.config.clientName
			cleoSmsMessage.fromUser = Me.config.sendMessageUser
			cleoSmsMessage.message = Me.config.itkInMessage
			cleoSmsMessage.mobileNumber = returnTelNumber
			cleoSmsMessage.productName = Me.config.productName
			cleoSmsMessage.referenceId = "CLEO_BRIS_CASE-" & docCall.Getitemvalue("CallNo")(0)
			
			Dim sendResponseHelper As New ResponseHelper()
			
			Set sendResponseHelper = Me.sendSms(cleoSmsMessage)
			
			'// Json Parsing...LS lib diamond of death...hence this crap...
			If Left(sendResponseHelper.data, 15) = |{"success":true| Then
				Call docCall.Replaceitemvalue("SMS_SENT", 1)
			Else
				Call docCall.Replaceitemvalue("SMS_SENT", 0)
			End If
			
			'// do we store ALL SMS against case for easy load???  Or retrieve 
			'// via api if viewing requied?
			'// Storing the "latest" message...is there any point???
			Call docCall.Replaceitemvalue("SMS_HAS", 1)
			Call docCall.Replaceitemvalue("SMS_LATEST_AT", Now)
			Call docCall.Replaceitemvalue("SMS_LATEST_USER", "ITK")	
			Call docCall.Replaceitemvalue("SMS_LATEST_MESSAGE", cleoSmsMessage.message)
			Call docCall.Replaceitemvalue("SMS_COUNT", 1)
			
			
			'//  This sets the grey "System Sent" comfort call icon.
			Call docCall.Replaceitemvalue("ComfortSentServiceTime", Now)
			
		Else
			Call Me.simpleLogger("CleoSms.processInboutnItk() SMS: send NOT required for service: " & callService)
		End If
		
SimpleExit:
		Exit Function
ErrorHandler:
		processInboutnItk.isSuccess = False
		processInboutnItk.uniqueIdentifier = "ERROR"
		processInboutnItk.message = "Error, see log."
		Call Me.simpleLogger("CleoSms.processInboutnItk() ERROR: " & Error)
		Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	
	Public Sub simpleLogger(message As String)
		
		If Me.s.Currentdatabase.Server = "" Then
			Print "CleoSms " & message
		Else
			MsgBox "CleoSms " & message
		End If
		
	End Sub
	
End Class


Public Function getCleoSmsInstance(docConfig As NotesDocument) As CleoSms
	On Error GoTo ErrorHandler
	
	Dim s As New NotesSession
	Dim dbCurrent As NotesDatabase
	Set dbCurrent = s.Currentdatabase


	If docConfig Is Nothing Then
		MsgBox "getCleoSmsInstance() B..."
		'// assume running in Calldb
		Set docConfig = dbCurrent.Getview("Setup").Getdocumentbykey("Database Configuration", True)
		Set docConfig = dbCurrent.Getview("Setup").Getfirstdocument()
		MsgBox "getCleoSmsInstance() C..."
		
		If docConfig Is Nothing Then
			MsgBox "getCleoSmsInstance() C...WTF!"
		Else 
			MsgBox "getCleoSmsInstance() D...Got config doc."
		End If
	End If
	
	If cleoSmsHelper Is Nothing Then
		
		Dim config As New CleoSmsConfig()
		
		config.debug = docConfig.Getitemvalue("CLEO_SMS_DEBUG")(0) <> "0"
		config.enabled = docConfig.Getitemvalue("CLEO_SMS_ENABLED")(0) = "1"
		config.logPayloads = docConfig.Getitemvalue("CLEO_SMS_LOGPAYLOADS")(0) = "1"	
		config.apiKey = docConfig.Getitemvalue("CLEO_SMS_APIKEY")(0)
		config.smsEndPoint = docConfig.Getitemvalue("CLEO_SMS_ENDPOINT")(0)
		config.sendPath = docConfig.Getitemvalue("CLEO_SMS_SENDPATH")(0)
		config.sendMessageUser = docConfig.Getitemvalue("CLEO_SMS_USER")(0)
		config.textCharsPerPage = docConfig.Getitemvalue("CLEO_SMS_CHARSPERPAGE")(0)
		config.textMaxChars = docConfig.Getitemvalue("CLEO_SMS_MAXSIZE")(0)
		
		config.productName = docConfig.Getitemvalue("CLEO_SMS_PRODUCTNAME")(0)
		config.clientName = docConfig.Getitemvalue("CLEO_SMS_CLIENTNAME")(0)
		
		config.itkInEnabled = docConfig.Getitemvalue("CLEO_SMS_ITK_IN")(0) = "1"
		config.itkInServices = docConfig.Getitemvalue("CLEO_SMS_ITK_SERVICES")
		config.itkInMessage = docConfig.Getitemvalue("CLEO_SMS_ITK_IN_MESSAGE")(0)
		
		
		Set config.dbCall = New NotesDatabase(dbCurrent.Server, docConfig.Getitemvalue("Config_Calls_Path")(0))
		Set config.dbSms = New NotesDatabase(dbCurrent.Server, docConfig.Getitemvalue("CLEO_SMS_DB_PATH")(0))
		
		Set cleoSmsHelper = New CleoSms(config)
		
	End If
	
	
	Set getCleoSmsInstance = cleoSmsHelper
	
SimpleExit:
	Exit Function
ErrorHandler:
	MsgBox "getCleoSmsInstance() ERROR: " & Error
	Call logErrorEx( "", {SEVERITY_HIGH}, Nothing )
	Resume SimpleExit
End Function




