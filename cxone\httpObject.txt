%REM
	Library HttpConnectionObject
	Created Apr 11, 2024 by Code Signer/sehnp
	Description: Comments for Library
%END REM
Option Public
Option Declare



%REM
	Class HttpConnectionObject
	Description: Comments for Class
%END REM
Public Class HttpConnectionObject
	
	Public isSuccess As Boolean
	Public responseBody As Variant
	Public responseStatus As Variant
	Public debug As Boolean
	Public fullPath As String
	
	
	%REM
		Sub New
		Description: Comments for Sub
	%END REM
	Public Sub New()
		Me.debug = false
	End Sub
	
	Public Function getConnectionObject() As Variant
		Dim httpObject As Variant
		
		'//	<STA>
		'		Set httpObject = CreateObject("MSXML2.XMLHTTP.6.0")
		'//	</STA>
		
		'		//	<LIVE>
		'				MsgBox ">>>>>>>>>>>>>>>>>>>>>>>>>> LIVE"
		Set httpObject = CreateObject("MSXML2.ServerXMLHTTP.6.0")				'//	Error: msxml6.dll: An Error occurred In the secure channel support  
		Call httpObject.setOption( 2, 13056 )	'//	ignore cert errors...!		'// MSXML2.XMLHTTP.6.0 does NOT support
		'		//	</LIVE>
		
		Set getConnectionObject = httpObject	
	End Function
	
	%REM
		Function doPost
		Description: Comments for Function
	%END REM
	Public Function doPostwithJwt(fullPath, payload, jwt) As Variant
		On Error GoTo ErrorHandler
		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() Start..."
		
		Me.fullPath = fullPath
		
		Dim httpObject As Variant
		Set httpObject = Me.getConnectionObject()
		
		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() Open (POST): " & fullPath
		Call httpObject.open("POST", fullPath, False)
		
		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() set headers..."
		Call httpObject.setRequestHeader("Content-Type", "application/json")
		httpObject.SetRequestHeader "Content-Length", Len(payload)
		
		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() jwt: " & jwt
		Call httpObject.setRequestHeader("authorization", "Bearer " & jwt)
		
		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() Send...payload: " & payload
		Call httpObject.send(payload & "")
		
		Me.responseStatus = Val(httpObject.status)
		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() responseStatus: " & Me.responseStatus
		
		Me.responseBody = httpObject.responseText
		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() responseBody: " & Me.responseBody
		
		If responseStatus < 200 Or responseStatus >= 300 Then
			Me.isSuccess = False
		Else
			Me.isSuccess = True
		End If
		
		'//	the calling process can handle the error.
		Set doPostwithJwt = httpObject
		
		If debug Then MsgBox "HttpConnectionObject.doPostwithJwt() Finish"
		
SimpleExit:
		Exit Function
ErrorHandler:
		MsgBox "HttpConnectionObject.doPostwithJwt() Line: " & Erl & ", Error: " & error
		Resume SimpleExit	
	End Function
	
	Public Function doGetWithJwt(fullPath, jwt) As Variant
		On Error GoTo ErrorHandler
		If Me.debug Then MsgBox "HttpConnectionObject.doGetWithJwt() Start..."
		
		Me.fullPath = fullPath
		
		jwt = Replace(jwt, " ", "")
		jwt = Replace(jwt, Chr(13), "")
		jwt = Replace(jwt, Chr(10), "")
		
		Dim httpObject As Variant
		Set httpObject = Me.getConnectionObject()
		
		'httpObject.setTimeouts Me.timeoutResolveMs, Me.timeoutConnectMs, Me.timeoutSendMs, Me.timeoutReceiveMs
		
		If Me.debug Then MsgBox "HttpConnectionObject.doGetWithJwt() Open (GET): " & fullPath
		Call httpObject.open("GET", fullPath, False)
		
		If Me.debug Then MsgBox "HttpConnectionObject.doGetWithJwt() set headers..."
		Call httpObject.setRequestHeader("Content-Type", "application/json")
		'		httpObject.SetRequestHeader "Content-Length", Len(payload)
		
		If jwt = "" Then
			If Me.debug Then MsgBox "HttpConnectionObject.doGetWithJwt() jwt: " & jwt
			Call httpObject.setRequestHeader("Authorization", "Bearer " & jwt)			
		End If

		
		'		If Me.debug Then MsgBox "HttpConnectionObject.doPostwithJwt() Send...payload: " & payload
		Call httpObject.send("")
		
		Me.responseStatus = Val(httpObject.status)
		If Me.debug Then MsgBox "HttpConnectionObject.doGetWithJwt() responseStatus: " & Me.responseStatus
		
		Me.responseBody = httpObject.responseText
		If Me.debug Then MsgBox "HttpConnectionObject.doGetWithJwt() responseBody: " & Me.responseBody
		
		If responseStatus < 200 Or responseStatus >= 300 Then
			Me.isSuccess = False
		Else
			Me.isSuccess = True
		End If
		
		'//	the calling process can handle the error.
		Set doGetWithJwt = httpObject
		
		If debug Then MsgBox "HttpConnectionObject.doGetWithJwt() Finish"
		
SimpleExit:
		Exit Function
ErrorHandler:
		MsgBox "HttpConnectionObject.doGetWithJwt() Line: " & Erl & ", Error: " & Error
		Resume SimpleExit	
	End Function
	
End Class