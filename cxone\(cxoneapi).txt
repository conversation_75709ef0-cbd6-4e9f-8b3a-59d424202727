%REM
	Agent cxonecreate
	Created Jun 16, 2025 by Code Signer/sehnp
	Description: Comments for Agent
%END REM
Option Public
Option Declare

Use "CxOneTelephony"

Sub Initialize
	
	
	On Error GoTo ErrorHandler
	
	Dim cw As New CleoWrapperApi()
	Dim cxOneTelephony As CxOneTelephony
	
	Dim session 			As New NotesSession
	Dim db 					As NotesDatabase
	Dim doc					As NotesDocument
	Dim s_q					As String
	Dim s_return_data		As String
	
	Dim je As New JsonEncoder()
	
	Dim sbLog As New StringBuffer(16)
	Dim cleoCallLowLevelUtility As New CleoCallLowLevelUtility()
	Dim responseHelper As New ResponseHelper()
	Dim sb As StringBuffer
	
	Dim debug As Boolean
	
	debug = True
	
	Set db = session.Currentdatabase
	Set doc = session.Documentcontext
	
	Set cxOneTelephony = getCxOneTelephonyInstance(cw.AppDB.getDatabaseDBConfig(Nothing))
	
	If doc.Hasitem("Request_Content") Then
		s_q = doc.Request_Content(0)		
	Else
		s_q = doc.Query_String(0)
	End If
	
	If cxOneTelephony.config.logPayloads Then
		Call logEvent("cxoneapi payload: " & s_q, "", Nothing)
	End If
	
	
	Dim action As String
	Dim callNo As String
	Dim userPrincipalName As String
	Dim phoneNo As String

	action = cleoCallLowLevelUtility.getUrlParamValue(s_q, "ACTION")
	callNo = cleoCallLowLevelUtility.getUrlParamValue(s_q, "CALLNO")
	
	phoneNo = cleoCallLowLevelUtility.getUrlParamValue(s_q, "PHONENO")
	
	If debug Then MsgBox "Agent cxonecreate ============== action: " & action
	
	Select Case action

			'//  Get a payload to "login" to socket server with.
		Case "GET_SOCKET_LOGIN_PAYLOAD":

			Set responseHelper = cxOneTelephony.getSocketLoginPayload()
			s_return_data = responseHelper.toJson()			
			
		'// Given a case number, return the payload sent in via Telephny app to preload a case
		'// for a Call Handler
		Case "GET_DEMOGRAPHICS":
			
			'// (cxoneapi)?openagent&ACTION=GET_DEMOGRAPHICS&CALLNO=3333
			Set responseHelper = cxOneTelephony.getNewCallDemographics(Val(callNo))
			s_return_data = responseHelper.toJson()	

			'// Get payload containing calls made for a case.
		Case "GET_CALLS_HISTORY_FOR_CASE":
			Set responseHelper = cxOneTelephony.getCallHistory(Val(callNo))
			s_return_data = responseHelper.toJson()
			
			'// Make outbound call via CXOne Platform
			'// (cxoneapi)?openagent&ACTION=CLICK_TO_DIAL_CX_ONE&CALLNO=3333&USERPRINCIPALNAME=<EMAIL>&PHONENO=+447567887364
		Case "CLICK_TO_DIAL_CX_ONE":
			
			'//  userPrincipalName = cleoCallLowLevelUtility.getUrlParamValue(s_q, "USERPRINCIPALNAME")
			
			Set responseHelper = cxOneTelephony.makeOutboundCall(Val(callNo), userPrincipalName, phoneNo)
			s_return_data = responseHelper.toJson()

			'// Get CXOne call history for a case
			'// (cxoneapi)?openagent&ACTION=GET_CXONE_CALL_HISTORY&CALLNO=3333
		Case "GET_CXONE_CALL_HISTORY":
			Set responseHelper = cxOneTelephony.getCxOneCallHistory(Val(callNo))
			s_return_data = responseHelper.toJson()


			'// this is most likely triggered in client, maybe this end point returns the url
		'//  to launch and/or does some further sort of checks???
		Case "CLICK_TO_DIAL_MS_TEAMS":
		
		Case Else
		
			s_return_data = |{"RESULT":"FAILURE", "MESSAGE": "ACTION NOT CODED FOR: | & action & |"}|
	End Select
	
	
	
	
SimpleExit:
	Print "Content-Type: application/json"
	Print "Cache-Control: no-cache"
	Print s_return_data

	MsgBox "Agent cxonecreate s_return_data: " & s_return_data

	Exit Sub
	
ErrorHandler:
	MsgBox "...agent...cxoneapi...ERROR: " & Error & {, line: } & Erl()
	s_return_data = |{"success": false, "message": "Error logged."}|
	Call logErrorEx({QueryString==>} & s_q,{SEVERITY_HIGH}, Nothing )
	Resume SimpleExit
End Sub