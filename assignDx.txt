function assignDx() {
  var moveApi = caseMoveApi();

  var prefix = "case-assign-dx--";

  var state = {
    data: {
      callNo: "",
      dxCodes: [],
      dialogReference: null
    },
    input: {
      dxCode: "",
      reason: ""
    }
  };

  function init() {
    //  get the call number from the page
    if (CallControllerClient.areWeInCallForm()) {
      state.data.callNo = CallControllerClient.m_call_id;
    } else {
      if (CGC.clickItem && CGC.clickItem.CallNo) {
        state.data.callNo = CGC.clickItem.CallNo;
      }
    }
  }

  function openDialog() {
    init();

    //  wrap it with div...jq puts a bunch of classes on the dialog.
    var html = "<div>" + getHtml() + "</div>";

    //   on opening of the dialog create click handlers for the select boxes
    //   load the classifications into the classification select box

    state.data.dialogReference = $(html).dialog({
      title: "Assign Priority",
      modal: true,
      resizable: false,
      width: 900,
      height: 300,
      open: function() {
        resetState();
        addClickHandlers();
        loadDxCodes();

        $(this).dialog("option", "position", "center");
      },
      close: function() {
        destroy();
        $(this)
          .dialog("destroy")
          .remove();
      },
      buttons: []
    });
    var x = 0;
  }

  /**
   *
   * @return {string}
   */
  function getHtml() {
    var html = [];
    html.push(
      "<div class='case-assign-to-base-wrapper ic24-flex-column ic24-gap--large'>"
    );

    /* Ensure the select box does not overflow */
    // select {
    //   width: 100%;
    //   max-width: 100%;
    //   white-space: nowrap;
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    // }

    // A select box we will load with bases
    html.push(
      "<select id='" +
      prefix +
      "select-dx' style='width: 100%;max-width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'>"
    );
    html.push("<option value=''>Select Priority</option>");
    html.push("</select>");

    html.push("<div>");
    // a text area for the user to enter a reason, make 100% width and 50px high
    // add a label for the text area
    html.push("<label for='" + prefix + "reason'>Reason (Optional)</label>");

    html.push(
      "<textarea id='" +
      prefix +
      "reason' style='width: 100%;height: 50px' placeholder=''></textarea>"
    );
    html.push("</div>");

    // A submit button
    html.push("<div class='ic24-flex-row ic24-justify-flex-space-between'>");
    html.push("<button id='" + prefix + "cancel'>Close</button>");
    html.push("<button id='" + prefix + "submit'>Submit</button>");
    html.push("</div>");

    // Add a div to write messages to
    html.push("<div id='" + prefix + "message'></div>");

    html.push("</div>");

    return html.join("");
  }

  function addClickHandlers() {
    // add a click handler to the submit button
    $("#" + prefix + "submit").on("click", function() {
      submit();
    });

    // add a click handler to the cancel button
    $("#" + prefix + "cancel").on("click", function() {
      cancel();
    });

    // add a click handler to the base select box
    $("#" + prefix + "select-dx").on("change", function() {
      state.input.dxCode = $(this).val();
    });

    // add a click handler to the reason text box
    $("#" + prefix + "reason").on("change", function() {
      state.input.reason = $(this).val();
    });
  }

  function removeClickHandlers() {
    $("#" + prefix + "submit").off("click");
    $("#" + prefix + "cancel").off("click");
    $("#" + prefix + "select-dx").off("change");
    $("#" + prefix + "reason").off("change");
  }

  function resetState() {
    state.input.dxCode = "";
    state.input.reason = "";
  }

  function loadDxCodes() {
    //CallControllerClient.showBusyMask(true);

    // disable the dorp down
    var dxSelect = $("#" + prefix + "select-dx");
    dxSelect.prop("disabled", true);

    moveApi.getDxCodes(state.data.callNo).then(function(response) {
      if (response.RESULT !== "SUCCESS") {
        writeMessage("Error loading dx codes: " + response.MESSAGE);
        return;
      }

      var dxCodes = response.DATA;

      state.data.dxCodes = dxCodes;

      // sort the dx codes by (Number) prop: Level1Mins
      dxCodes.sort(function(a, b) {
        return a.Level1Mins - b.Level1Mins;
      });

      var html = [];
      html.push("<option value=''>Select Dx</option>");

      // loop through the bases and add them to the select box
      for (var dxCode in dxCodes) {
        html.push(
          "<option value='" +
          dxCodes[dxCode].DxCode +
          "'>" +
          dxCodes[dxCode].DxCode +
          " - " +
          dxCodes[dxCode].Priority_Label +
          ", " +
          dxCodes[dxCode].Description +
          "</option>"
        );
      }
      // $("#" + prefix + "select-dx").html(html.join(""));
      //  Load the select box with the dx codes
      dxSelect.html(html.join(""));
      dxSelect.prop("disabled", false);
      // CallControllerClient.showBusyMask(false);
    });
  }

  function validate() {
    if (state.input.dxCode === "") {
      writeMessage("Please select a priority.");
      return false;
    }
    // if (state.input.reason === "") {
    //   writeMessage("Please enter a reason.");
    //   return false;
    // }
    return true;
  }

  function cancel() {
    state.data.dialogReference.dialog("close");
  }

  /**
   *
   */
  function submit() {
    if (!validate()) {
      return;
    }

    // disable submit and cancel buttons
    $("#" + prefix + "submit").prop("disabled", true);
    $("#" + prefix + "cancel").prop("disabled", true);

    CallControllerClient.showBusyMask(true);
    var promise = moveApi.assignDx(state);
    promise.then(function(response) {
      // handle response
      if (response.RESULT === "SUCCESS") {
        writeMessage("Priority assigned successfully to: " + state.data.callNo);
        $("#" + prefix + "submit").prop("disabled", true);

        //  refresh the view
        autoRefreshView();
      } else {
        // handle error
        //  {"RESULT":"FAILURE", "MESSAGE": "blah, blah, blah"}
        if (response.RESULT === "FAILURE") {
          writeMessage("Priority assigned failed, " + response.MESSAGE);
        } else {
          writeMessage("Priority assigned failed.");
        }
      }
    });

    //  Because HCL IDE...suuuuuccckkkks.
    promise["catch"](function(error) {
      // handle error
      alert("ERROR moving case: " + error + "");
    });

    promise["finally"](function() {
      CallControllerClient.showBusyMask(false);
      $("#" + prefix + "cancel").prop("disabled", false);
    });
  }

  function writeMessage(message) {
    $("#" + prefix + "message").html(message);
  }

  function rightClickMenu(hasPermission) {
    var ClickItem = $("[id='ctx_assign-dx']");
    ClickItem.hide();
    if (hasPermission) {
      // if (CallControllerClient.hasGlobalPermission("ASSIGN_DX")) {
      // if (CGC.clickItem.ALL_VIEW_INCLUDE !== "OVERSIGHT_FOLLOW_UP") {
      //   return;
      // }

      ClickItem.show();
    }
  }

  function destroy() {
    removeClickHandlers();
    CallControllerClient.showBusyMask(false);
  }

  return {
    openDialog: openDialog,
    rightClickMenu: rightClickMenu
  };
}
